const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 新增配置部分开始
  chainWebpack: config => {
    // 添加文件类型处理规则
    config.module
      .rule('office-files')
      .test(/\.(docx?|xlsx?|apk?|pdf|txt|zip|rar)$/i)    // 扩展支持更多格式
      .use('file-loader')
      .loader('file-loader')
      .options({
        name: 'files/[name].[hash:8].[ext]', // 带哈希的文件名
        esModule: false // 关闭ES模块语法
      })
      .end()
    
    // 可选：添加SVG排除规则（避免与默认规则冲突）
    config.module
      .rule('svg')
        .exclude
          .add(/\.docx?$/)
          .end()
  },
  
  // 高级配置（可选）
  configureWebpack: {
    performance: {
      maxAssetSize: 1024 * 1024 * 5, // 5MB文件大小限制
      maxEntrypointSize: 1024 * 1024 * 5
    }
  }
  // 新增配置部分结束
})