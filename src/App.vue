<template>
  <div class="container">
    <!-- 原有图片展示部分保持不变 -->
    <h1>微型图片库</h1>
    <div class="gallery">
      <div v-for="(image, index) in images" :key="index" class="image-item">
        <div class="image-wrapper">
          <img :src="image.url" :alt="image.name" class="image" loading="lazy">
        </div>
        <div class="image-name">{{ image.name }}</div>
      </div>
    </div>

    <!-- 新增文件下载列表 -->
    <h1 style="margin-top: 40px;">文件下载</h1>
    <div class="file-list">
    <a 
      v-for="(file, index) in files" 
      :key="index"
      :href="file.url"
      download
      class="file-item"
    >
      📄 {{ file.name }}
    </a>
  </div>
  </div>
</template>

<script>
  import { ref, onMounted } from 'vue'

  export default {
    setup() {
      const images = ref([])
      const files = ref([])

      // 自动加载 public/img 目录下的图片
      const loadImages = () => {
        const context = require.context(
          '/img', // 图片目录路径（相对于 public 目录）
          false, // 是否遍历子目录
          /\.(png|jpe?g|gif|webp)$/i // 匹配的文件类型
        )

        const loadedImages = context.keys().map((key) => ({
          url: context(key),
          name: key.replace('./', '') // 去除路径前缀
        }))

        images.value = loadedImages
      }
   // 修复后的文件加载方法
   const loadFiles = () => {
    const context = require.context(
      '/file', // 使用loader处理
      false,
      /\.(docx?|xlsx?|pdf|txt|zip|rar)$/i
    )

    files.value = context.keys().map(key => {
      const fileName = key.replace('./', '')
      return {
        name: fileName,
        // 生成带哈希的文件路径
        url: context(key)
      }
    })
  }


      onMounted(() => {
        loadImages()
        loadFiles() // 新增文件加载调用
      })

      return {
        images,
        files
      }
    }
  }
</script>

<style scoped>
  .gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    justify-content: flex-start;
  }

  .image-item {
    width: 100px;
    /* 固定容器宽度 */
    margin-bottom: 8px;
  }

  .image-wrapper {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
  }

  .image {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    transition: transform 0.2s;
  }

  .image:hover {
    transform: scale(1.05);
    cursor: zoom-in;
  }

  .image-name {
    font-size: 0.75em;
    color: #666;
    text-align: center;
    padding: 4px 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 新增文件列表样式 */
  .file-list {
    max-width: 800px;
    margin: 20px auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .file-item {
    padding: 12px;
    border-bottom: 1px solid #eee;
  }

  .file-item:last-child {
    border-bottom: none;
  }

  .file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .file-name {
    font-size: 0.95em;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .download-btn {
    background: #007bff;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.9em;
    transition: background 0.2s;
  }

  .download-btn:hover {
    background: #0056b3;
  }
</style>